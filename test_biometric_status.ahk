#Requires AutoHotkey v2.0

; Test script for CheckAndUpdateBiometricStatus function
; This script tests the new data-driven biometric status logic

; Include necessary files
#Include lib\PathManager.ahk

; Simple output function
TestOutput(message) {
    OutputDebug(message)
    ; Also write to a simple text file
    try {
        FileAppend(message "`n", "test_results.txt")
    } catch {
        ; Ignore file errors
    }
}

; Test function to verify CheckAndUpdateBiometricStatus
TestBiometricStatus() {
    ; Test cases
    testCases := [
        {rollNumber: "9350", description: "Regular candidate with ThumbPreference=Both, all verified"},
        {rollNumber: "9351", description: "Special candidate with ThumbPreference=Right, right thumb verified"},
        {rollNumber: "9352", description: "Candidate with no verification status"}
    ]

    ; Load the main script functions (simplified)
    ; We'll create a minimal version of LoadCandidateData for testing

    for testCase in testCases {
        TestOutput("Testing: " testCase.description)

        ; Test LoadCandidateData
        candidateData := TestLoadCandidateData(testCase.rollNumber)
        if (candidateData.Name != "") {
            TestOutput("Loaded candidate: " candidateData.Name)
            TestOutput("ThumbPreference: " candidateData.ThumbPreference)
            TestOutput("PhotoStatus: " candidateData.PhotoStatus)
            TestOutput("FingerprintStatus: " candidateData.FingerprintStatus)
            TestOutput("RightFingerprintStatus: " candidateData.RightFingerprintStatus)
            TestOutput("SignatureStatus: " candidateData.SignatureStatus)

            ; Test the biometric status logic
            result := TestCheckBiometricStatus(testCase.rollNumber, candidateData)
            TestOutput("Biometric status result: " result)
        } else {
            TestOutput("Could not load candidate data for " testCase.rollNumber)
        }

        TestOutput("---")
    }
}

; Simplified LoadCandidateData for testing
TestLoadCandidateData(rollNumber) {
    try {
        ; Get database file path
        candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

        ; Check if file exists
        if (!FileExist(candidatesPath)) {
            TestOutput("ERROR: Candidates file not found: " candidatesPath)
            return {}
        }

        ; Check if candidate section exists
        testRead := IniRead(candidatesPath, rollNumber, "Name", "")
        if (testRead == "") {
            TestOutput("DEBUG: Candidate not found: " rollNumber)
            return {}
        }

        ; Read candidate data fields
        candidateData := {}
        candidateData.RollNumber := rollNumber
        candidateData.Name := IniRead(candidatesPath, rollNumber, "Name", "")
        candidateData.Special := IniRead(candidatesPath, rollNumber, "Special", "0")
        candidateData.ThumbPreference := IniRead(candidatesPath, rollNumber, "ThumbPreference", "")
        candidateData.PhotoStatus := IniRead(candidatesPath, rollNumber, "PhotoStatus", "")
        candidateData.FingerprintStatus := IniRead(candidatesPath, rollNumber, "FingerprintStatus", "")
        candidateData.RightFingerprintStatus := IniRead(candidatesPath, rollNumber, "RightFingerprintStatus", "")
        candidateData.SignatureStatus := IniRead(candidatesPath, rollNumber, "SignatureStatus", "")
        candidateData.BiometricStatus := IniRead(candidatesPath, rollNumber, "BiometricStatus", "")

        return candidateData

    } catch as err {
        TestOutput("ERROR: Error loading candidate data for " rollNumber ": " err.Message)
        return {}
    }
}

; Test the biometric status logic
TestCheckBiometricStatus(rollNumber, candidateData) {
    try {
        ; Simulate SignatureVerificationEnabled = false for testing
        SignatureVerificationEnabled := false

        ; Check photo verification (always required)
        photoVerified := (candidateData.PhotoStatus == "Verified")

        ; Check signature verification (if enabled)
        signatureVerified := (!SignatureVerificationEnabled || candidateData.SignatureStatus == "Verified")

        ; Check thumbprint verification based on ThumbPreference
        thumbPreference := candidateData.ThumbPreference
        if (thumbPreference == "") {
            thumbPreference := "Both"  ; Default to Both if not set
        }

        leftThumbVerified := (candidateData.FingerprintStatus == "Saved" || candidateData.FingerprintStatus == "Verified")
        rightThumbVerified := (candidateData.RightFingerprintStatus == "Saved" || candidateData.RightFingerprintStatus == "Verified")

        thumbprintVerified := false
        if (thumbPreference == "Both") {
            ; Both thumbs must be verified
            thumbprintVerified := (leftThumbVerified && rightThumbVerified)
            TestOutput("DEBUG: ThumbPreference=Both: left=" leftThumbVerified ", right=" rightThumbVerified ", result=" thumbprintVerified)
        } else if (thumbPreference == "Left") {
            ; Only left thumb must be verified
            thumbprintVerified := leftThumbVerified
            TestOutput("DEBUG: ThumbPreference=Left: left=" leftThumbVerified ", result=" thumbprintVerified)
        } else if (thumbPreference == "Right") {
            ; Only right thumb must be verified
            thumbprintVerified := rightThumbVerified
            TestOutput("DEBUG: ThumbPreference=Right: right=" rightThumbVerified ", result=" thumbprintVerified)
        } else {
            ; Unknown preference, default to Both
            thumbprintVerified := (leftThumbVerified && rightThumbVerified)
            TestOutput("DEBUG: ThumbPreference=" thumbPreference " (unknown), defaulting to Both, result=" thumbprintVerified)
        }

        ; Determine overall verification status
        allVerified := (photoVerified && thumbprintVerified && signatureVerified)

        TestOutput("DEBUG: Final verification: photo=" photoVerified ", thumbs=" thumbprintVerified ", signature=" signatureVerified ", all=" allVerified)

        return allVerified ? "Verified" : "Incomplete"

    } catch as err {
        TestOutput("ERROR: Error in TestCheckBiometricStatus: " err.Message)
        return "Error"
    }
}

; Run the test
TestOutput("Starting biometric status test...")
TestBiometricStatus()
TestOutput("Test completed. Check test_results.txt for results.")

; Show results
MsgBox("Test completed. Check test_results.txt for results.", "Test Complete")
