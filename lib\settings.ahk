#Requires AutoHotkey v2.0

; Include path manager
#Include PathManager.ahk

; ===================================================================
; WinCBT-Biometric Settings Dialog
; Provides a comprehensive interface for configuring application settings
;
; Used by: WinCBT-Biometric
;
; This file is specific to WinCBT-Biometric and is not shared with
; WinCBT-Admin, which uses its own admin_settings.ahk file.
; ===================================================================

; ; ShowSettingsDialog()
; ; Shows a dialog for configuring application settings
; ; @return: True if settings were saved, False otherwise
ShowSettingsDialog() {
    global myGui  ; Reference to the main GUI from WinCBT-Biometric.ahk

    ; Create the dialog as a child of the main GUI
    settingsGui := Gui("+AlwaysOnTop +Owner" . myGui.Hwnd, "Application Settings")
    settingsGui.SetFont("s10", "Segoe UI")

    ; Disable the main GUI while settings dialog is open
    myGui.Opt("+Disabled")

    ; Get configuration file path using PathManager
    configFile := PathManager.ConfigFile

    ; Create tabs for different setting categories
    Tabs := settingsGui.Add("Tab3", "x10 y10 w580 h430", ["General", "Verification", "Camera", "Paths"])

    ; ==================== GENERAL TAB ====================
    Tabs.UseTab(1)
    settingsGui.Add("GroupBox", "x20 y40 w560 h120", "General Settings")

    ; Log Level
    settingsGui.Add("Text", "x30 y70 w120 h20", "Log Level:")
    LogLevelCombo := settingsGui.Add("ComboBox", "x160 y70 w150", ["Debug", "Info", "Warning", "Error"])
    currentLogLevel := IniRead(configFile, "Settings", "LogLevel", "Info")
    LogLevelCombo.Text := currentLogLevel

    ; Auto Approve
    settingsGui.Add("Text", "x30 y100 w120 h20", "Auto Approve:")
    AutoApproveCheckbox := settingsGui.Add("Checkbox", "x160 y100 w20 h20")
    AutoApproveCheckbox.Value := IniRead(configFile, "Settings", "AutoApprove", "0") = "1" ? 1 : 0

    ; Random Seat Assignment
    settingsGui.Add("Text", "x30 y130 w120 h20", "Random Seats:")
    RandomSeatCheckbox := settingsGui.Add("Checkbox", "x160 y130 w20 h20")
    RandomSeatCheckbox.Value := IniRead(configFile, "Settings", "RandomSeatAssignment", "1") = "1" ? 1 : 0

    ; ==================== VERIFICATION TAB ====================
    Tabs.UseTab(2)
    settingsGui.Add("GroupBox", "x20 y40 w560 h120", "Verification Options")

    ; Signature Verification
    settingsGui.Add("Text", "x30 y70 w150 h20", "Signature Verification:")
    SignatureVerificationCheckbox := settingsGui.Add("Checkbox", "x190 y70 w20 h20")
    SignatureVerificationCheckbox.Value := IniRead(configFile, "Verification", "SignatureVerification", "1") = "1" ? 1 : 0

    ; Right Thumbprint Verification
    settingsGui.Add("Text", "x30 y100 w150 h20", "Right Thumbprint:")
    RightThumbprintCheckbox := settingsGui.Add("Checkbox", "x190 y100 w20 h20")
    RightThumbprintCheckbox.Value := IniRead(configFile, "Verification", "RightThumbprintVerification", "1") = "1" ? 1 : 0

    ; Verification Modes
    settingsGui.Add("GroupBox", "x20 y170 w560 h120", "Verification Modes")

    ; Photo Verification Mode
    settingsGui.Add("Text", "x30 y200 w150 h20", "Photo Verification:")
    PhotoModeCombo := settingsGui.Add("ComboBox", "x190 y200 w150", ["Auto", "Manual", "Both"])
    PhotoModeCombo.Text := IniRead(configFile, "Verification", "PhotoVerificationMode", "Auto")

    ; Signature Verification Mode
    settingsGui.Add("Text", "x30 y230 w150 h20", "Signature Verification:")
    SignatureModeCombo := settingsGui.Add("ComboBox", "x190 y230 w150", ["Auto", "Manual", "Both"])
    SignatureModeCombo.Text := IniRead(configFile, "Verification", "SignatureVerificationMode", "Auto")

    ; Fingerprint Verification Mode
    settingsGui.Add("Text", "x30 y260 w150 h20", "Fingerprint Verification:")
    FingerprintModeCombo := settingsGui.Add("ComboBox", "x190 y260 w150", ["Auto", "Manual", "Both"])
    FingerprintModeCombo.Text := IniRead(configFile, "Verification", "FingerprintVerificationMode", "Auto")

    ; Confidence Thresholds
    settingsGui.Add("GroupBox", "x20 y300 w560 h130", "Confidence Thresholds")

    ; Photo Confidence
    settingsGui.Add("Text", "x30 y330 w150 h20", "Photo Confidence:")
    PhotoConfidenceEdit := settingsGui.Add("Edit", "x190 y330 w60 h20", IniRead(configFile, "Verification", "PhotoConfidenceThreshold", "85"))
    settingsGui.Add("Text", "x260 y330 w300 h20", "% (Skip manual verification above this threshold)")

    ; Signature Confidence
    settingsGui.Add("Text", "x30 y360 w150 h20", "Signature Confidence:")
    SignatureConfidenceEdit := settingsGui.Add("Edit", "x190 y360 w60 h20", IniRead(configFile, "Verification", "SignatureConfidenceThreshold", "80"))
    settingsGui.Add("Text", "x260 y360 w300 h20", "% (Skip manual verification above this threshold)")

    ; Fingerprint Confidence
    settingsGui.Add("Text", "x30 y390 w150 h20", "Fingerprint Confidence:")
    FingerprintConfidenceEdit := settingsGui.Add("Edit", "x190 y390 w60 h20", IniRead(configFile, "Verification", "FingerprintConfidenceThreshold", "90"))
    settingsGui.Add("Text", "x260 y390 w300 h20", "% (Skip manual verification above this threshold)")

    ; ==================== CAMERA TAB ====================
    Tabs.UseTab(3)
    settingsGui.Add("GroupBox", "x20 y40 w560 h120", "Camera Settings")

    ; Camera Selection
    settingsGui.Add("Text", "x30 y70 w120 h20", "Camera Device:")
    CameraCombo := settingsGui.Add("ComboBox", "x160 y70 w300 h200")

    ; Detect Cameras Button
    ButtonDetectCameras := settingsGui.Add("Button", "x470 y70 w100 h25 +0x1000", "Detect Cameras")

    ; Camera Status
    settingsGui.Add("Text", "x30 y100 w120 h20", "Status:")
    CameraStatusText := settingsGui.Add("Text", "x160 y100 w400 h20", "Click 'Detect Cameras' to scan for available devices")

    ; Add instruction text
    settingsGui.Add("Text", "x160 y125 w400 h40", "Note: If no cameras appear in the dropdown, click the 'Detect Cameras' button to scan for connected devices. Make sure your camera is properly connected before scanning.")

    ; ==================== PATHS TAB ====================
    Tabs.UseTab(4)
    settingsGui.Add("GroupBox", "x20 y40 w560 h120", "Database Path")

    ; Database Path
    settingsGui.Add("Text", "x30 y70 w120 h20", "Database Path:")
    DBPathEdit := settingsGui.Add("Edit", "x160 y70 w300 h20", IniRead(configFile, "Paths", "dbPath", "db"))
    ButtonBrowseDB := settingsGui.Add("Button", "x470 y70 w100 h25 +0x1000", "Browse...")

    ; ==================== BUTTONS ====================
    Tabs.UseTab()
    ButtonSave := settingsGui.Add("Button", "x370 y450 w100 h30 +0x1000", "Save")
    ButtonCancel := settingsGui.Add("Button", "x480 y450 w100 h30 +0x1000", "Cancel")

    ; ==================== EVENT HANDLERS ====================

    ; Camera detection handler
    ButtonDetectCameras.OnEvent("Click", DetectCamerasHandler)

    ; Browse button handler
    ButtonBrowseDB.OnEvent("Click", BrowseDBHandler)

    ; Save button handler
    ButtonSave.OnEvent("Click", SaveSettings)

    ; Cancel button handler
    ButtonCancel.OnEvent("Click", CancelDialog)

    ; Handle dialog close
    settingsGui.OnEvent("Close", CancelDialog)

    ; ==================== HANDLER FUNCTIONS ====================

    ; Detect cameras function
    DetectCamerasHandler(*) {
        ; Clear the combo box
        CameraCombo.Delete()

        ; Add a loading indicator to the dropdown
        CameraCombo.Add(["Detecting cameras... Please wait..."])
        CameraCombo.Choose(1)

        ; Disable the combo box and button during detection
        CameraCombo.Enabled := false
        ButtonDetectCameras.Enabled := false

        ; Update status
        CameraStatusText.Text := "Detecting cameras... Please wait..."
        OutputDebug("Starting camera detection")

        ; Force GUI update to show the loading indicator
        Sleep(50)

        ; Detect cameras using the global function
        cameras := DetectCameras()

        ; Clear the loading indicator
        CameraCombo.Delete()

        ; Add cameras to the combo box
        if (cameras.Length > 0) {
            selectedIndex := 1  ; Default to first camera

            ; Read from the new [Camera] section
            currentCamera := IniRead(configFile, "Camera", "CameraName", "")

            ; If not found, try the old section for backward compatibility
            if (currentCamera == "") {
                currentCamera := IniRead(configFile, "BiometricDevices", "Camera", "")
                OutputDebug("Camera name not found in [Camera] section, using value from [BiometricDevices]: " currentCamera)
            } else {
                OutputDebug("Using camera name from [Camera] section: " currentCamera)
            }

            OutputDebug("DetectCameras: Found " cameras.Length " cameras")
            for i, camera in cameras {
                CameraCombo.Add([camera])
                OutputDebug("Added camera " i ": " camera)

                ; Check if this is the current camera
                if (camera == currentCamera) {
                    selectedIndex := i
                    OutputDebug("Current camera matched at index " i)
                }
            }

            ; Select the current camera or the first one
            CameraCombo.Choose(selectedIndex)
            OutputDebug("Selected camera index: " selectedIndex)

            ; Update status
            CameraStatusText.Text := cameras.Length . " camera(s) found."
        } else {
            ; No cameras found
            CameraCombo.Add(["No cameras detected"])
            CameraCombo.Choose(1)
            CameraStatusText.Text := "No cameras detected. Make sure your camera is connected and try again."
            OutputDebug("No cameras detected")
        }

        ; Re-enable the controls
        CameraCombo.Enabled := true
        ButtonDetectCameras.Enabled := true
    }

    ; Browse for database path
    BrowseDBHandler(*) {
        selectedFolder := DirSelect("*" A_ScriptDir, 3, "Select Database Folder")
        if (selectedFolder) {
            ; Make path relative to script directory if it's a subfolder
            if (InStr(selectedFolder, A_ScriptDir) = 1) {
                relativePath := SubStr(selectedFolder, StrLen(A_ScriptDir) + 2)
                DBPathEdit.Text := relativePath
            } else {
                DBPathEdit.Text := selectedFolder
            }
        }
    }

    ; Save settings function
    SaveSettings(*) {
        ; Save General settings
        IniWrite(LogLevelCombo.Text, configFile, "Settings", "LogLevel")
        IniWrite(AutoApproveCheckbox.Value ? "1" : "0", configFile, "Settings", "AutoApprove")
        IniWrite(RandomSeatCheckbox.Value ? "1" : "0", configFile, "Settings", "RandomSeatAssignment")

        ; Save Verification settings
        IniWrite(SignatureVerificationCheckbox.Value ? "1" : "0", configFile, "Verification", "SignatureVerification")
        IniWrite(RightThumbprintCheckbox.Value ? "1" : "0", configFile, "Verification", "RightThumbprintVerification")
        IniWrite(PhotoModeCombo.Text, configFile, "Verification", "PhotoVerificationMode")
        IniWrite(SignatureModeCombo.Text, configFile, "Verification", "SignatureVerificationMode")
        IniWrite(FingerprintModeCombo.Text, configFile, "Verification", "FingerprintVerificationMode")
        IniWrite(PhotoConfidenceEdit.Text, configFile, "Verification", "PhotoConfidenceThreshold")
        IniWrite(SignatureConfidenceEdit.Text, configFile, "Verification", "SignatureConfidenceThreshold")
        IniWrite(FingerprintConfidenceEdit.Text, configFile, "Verification", "FingerprintConfidenceThreshold")

        ; Save Camera settings if a camera is selected
        if (CameraCombo.Value > 0) {
            selectedCamera := CameraCombo.Text
            cameraIndex := CameraCombo.Value - 1  ; Convert to 0-based index

            ; Save to the new [Camera] section
            IniWrite(selectedCamera, configFile, "Camera", "CameraName")
            IniWrite(cameraIndex, configFile, "Camera", "CameraIndex")
        }

        ; Save Paths settings
        IniWrite(DBPathEdit.Text, configFile, "Paths", "dbPath")

        ; Show success message
        MsgBox("Settings have been saved successfully.`n`nSome changes may require restarting the application to take effect.", "Settings Saved", "262208")

        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Close the dialog
        settingsGui.Destroy()
    }

    ; Cancel dialog function
    CancelDialog(*) {
        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Destroy the dialog
        settingsGui.Destroy()
        OutputDebug("Settings dialog closed without saving")
    }

    ; Show the dialog immediately without waiting for camera detection
    settingsGui.Show("w600 h500")

    ; Make dialog modal
    WinWaitClose("ahk_id " settingsGui.Hwnd)

    ; Return true if the dialog was closed with the Save button
    return FileExist(configFile)
}

; ; DetectCameras()
; ; Detects available camera devices using FFmpeg only
; ; @return: Array of camera names
DetectCameras() {
    cameras := []

    ; Use FFmpeg to list DirectShow devices
    ffmpegPath := PathManager.GetPath("Bin") "ffmpeg.exe"
    if (!FileExist(ffmpegPath)) {
        OutputDebug("FFmpeg executable not found at: " ffmpegPath)
        MsgBox("FFmpeg executable not found at: " ffmpegPath "`n`nPlease make sure FFmpeg is installed in the bin directory.", "Camera Detection Error", "Icon!")
        return cameras
    }

    ; Create a temporary file to store the output
    tempFile := PathManager.GetTempFilePath("camera_list", "txt")

    ; Ensure the temporary file doesn't exist
    if (FileExist(tempFile))
        FileDelete(tempFile)

    ; Run FFmpeg with the DirectShow device list command using cmd.exe to ensure proper redirection
    ffmpegCmd := ffmpegPath . " -list_devices true -f dshow -i dummy"
    cmdLine := A_ComSpec . " /c " . ffmpegCmd . " > `"" . tempFile . "`" 2>&1"

    try {
        ; Run the command and wait for it to complete
        OutputDebug("Running command: " . cmdLine)
        RunWait(cmdLine, , "Hide")

        ; Read the output file
        if (FileExist(tempFile)) {
            OutputDebug("Reading output file: " . tempFile)
            fileContent := FileRead(tempFile)

            ; Debug output
            OutputDebug("File content length: " . StrLen(fileContent))

            ; Parse the output to find camera names
            ; FFmpeg lists devices with lines like: [dshow @ 000001e2542c7b40] "HD Pro Webcam C920" (video)
            ; followed by lines with "Alternative name" containing the device path
            inVideoDevices := true  ; Assume we're in the video devices section by default
            videoDeviceName := ""

            Loop Parse, fileContent, "`n", "`r" {
                OutputDebug("Parsing line: " . A_LoopField)

                ; Check for video device section
                if (InStr(A_LoopField, "DirectShow video devices")) {
                    OutputDebug("Found video devices section")
                    inVideoDevices := true
                    continue
                }

                ; Stop when we reach audio devices section
                if (InStr(A_LoopField, "DirectShow audio devices") || InStr(A_LoopField, "(audio)")) {
                    OutputDebug("Found audio devices section, stopping")
                    inVideoDevices := false
                    continue
                }

                ; Extract camera name directly from the dshow output line
                ; Format: [dshow @ XXXXXXXX] "Camera Name" (video)
                if (InStr(A_LoopField, "(video)")) {
                    ; Extract the camera name between quotes
                    if (RegExMatch(A_LoopField, "\] `"(.+)`" \(video\)", &match)) {
                        videoDeviceName := match[1]
                        OutputDebug("Found video device: " . videoDeviceName)
                        cameras.Push(videoDeviceName)
                    } else if (RegExMatch(A_LoopField, "`"(.+)`" \(video\)", &match)) {
                        videoDeviceName := match[1]
                        OutputDebug("Found video device (alt method): " . videoDeviceName)
                        cameras.Push(videoDeviceName)
                    } else {
                        ; Try a more general approach to extract text between quotes
                        startPos := InStr(A_LoopField, "`"")
                        if (startPos > 0) {
                            endPos := InStr(A_LoopField, "`"", false, startPos + 1)
                            if (endPos > startPos) {
                                videoDeviceName := SubStr(A_LoopField, startPos + 1, endPos - startPos - 1)
                                OutputDebug("DetectCameras: Found video device (general method): " . videoDeviceName)
                                cameras.Push(videoDeviceName)
                            }
                        }
                    }
                }
            }

            ; Delete the temporary file
            try {
                FileDelete(tempFile)
                OutputDebug("Deleted temporary file")
            } catch Error as e {
                OutputDebug("Error deleting temporary file: " . e.Message)
            }
        } else {
            OutputDebug("Output file not found: " . tempFile)
        }
    } catch Error as e {
        OutputDebug("Error detecting cameras: " . e.Message)
    }

    ; Log the detected cameras
    OutputDebug("Detected " . cameras.Length . " cameras")
    for i, camera in cameras {
        OutputDebug("Camera " . i . ": " . camera)
    }

    return cameras
}
