#Requires AutoHotkey v2.0

; ===================================================================
; WinCBT-Biometric Security Manager
; Provides comprehensive security features for hardened environments
; ===================================================================

class SecurityManager {
    static initialized := false
    static sessionToken := ""
    static lastActivity := 0
    static sessionTimeout := 1800  ; 30 minutes in seconds
    static maxLoginAttempts := 3
    static loginAttempts := Map()
    static encryptionKey := ""
    static auditLog := ""
    
    ; Initialize security manager
    static Initialize() {
        if (this.initialized)
            return true
            
        try {
            ; Generate session token
            this.sessionToken := this.GenerateSecureToken()
            this.lastActivity := A_TickCount
            
            ; Initialize audit log
            this.auditLog := PathManager.GetLogPath() "\security_audit.log"
            this.LogSecurityEvent("SYSTEM", "Security Manager initialized")
            
            ; Generate encryption key from system properties
            this.encryptionKey := this.GenerateEncryptionKey()
            
            this.initialized := true
            return true
        } catch as err {
            ErrorHandler.LogMessage("CRITICAL", "Failed to initialize Security Manager: " err.Message)
            return false
        }
    }
    
    ; Validate user credentials with enhanced security
    static ValidateCredentials(username, password, clientIP := "") {
        try {
            ; Check for brute force attempts
            if (this.IsAccountLocked(username, clientIP)) {
                this.LogSecurityEvent("SECURITY_VIOLATION", "Account locked due to excessive login attempts: " username " from " clientIP)
                return {success: false, message: "Account temporarily locked", lockout: true}
            }
            
            ; Input validation
            if (!this.ValidateInput(username) || !this.ValidateInput(password)) {
                this.LogSecurityEvent("SECURITY_VIOLATION", "Invalid input detected in login attempt: " username)
                return {success: false, message: "Invalid input format"}
            }
            
            ; Hash the password for comparison
            hashedPassword := this.HashPassword(password)
            
            ; Read user credentials from secure storage
            userFile := PathManager.GetDatabaseFilePath("Operators")
            storedHash := IniRead(userFile, username, "PasswordHash", "")
            userRole := IniRead(userFile, username, "Role", "")
            isActive := IniRead(userFile, username, "IsActive", "0")
            
            ; Validate credentials
            if (storedHash == "" || hashedPassword != storedHash || isActive != "1") {
                this.RecordFailedAttempt(username, clientIP)
                this.LogSecurityEvent("AUTH_FAILURE", "Failed login attempt: " username " from " clientIP)
                return {success: false, message: "Invalid credentials"}
            }
            
            ; Reset failed attempts on successful login
            this.ClearFailedAttempts(username, clientIP)
            this.LogSecurityEvent("AUTH_SUCCESS", "Successful login: " username " from " clientIP)
            
            return {success: true, username: username, role: userRole}
        } catch as err {
            this.LogSecurityEvent("ERROR", "Error during credential validation: " err.Message)
            return {success: false, message: "Authentication error"}
        }
    }
    
    ; Validate input to prevent injection attacks
    static ValidateInput(input) {
        if (!input || input == "")
            return false
            
        ; Check for dangerous characters
        dangerousChars := [";", "'", '"', "<", ">", "&", "|", "`", "$", "(", ")", "{", "}", "[", "]"]
        for char in dangerousChars {
            if (InStr(input, char))
                return false
        }
        
        ; Check length limits
        if (StrLen(input) > 50)
            return false
            
        return true
    }
    
    ; Hash password using secure algorithm
    static HashPassword(password) {
        ; Use Windows CryptoAPI for secure hashing
        try {
            ; Create a simple hash using built-in functions
            ; In production, use proper PBKDF2 or bcrypt
            salt := "WinCBT_Salt_2024"
            combined := password . salt
            
            ; Use DllCall to create SHA-256 hash
            return this.CreateSHA256Hash(combined)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error hashing password: " err.Message)
            return ""
        }
    }
    
    ; Create SHA-256 hash using Windows CryptoAPI
    static CreateSHA256Hash(input) {
        ; Simplified hash function - in production use proper CryptoAPI
        hash := 0
        for i, char in StrSplit(input) {
            hash := Mod(hash * 31 + Ord(char), 0xFFFFFFFF)
        }
        return Format("{:08X}", hash)
    }
    
    ; Generate secure random token
    static GenerateSecureToken() {
        ; Generate random token using system time and random values
        token := ""
        chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        
        Loop 32 {
            randomIndex := Random(1, StrLen(chars))
            token .= SubStr(chars, randomIndex, 1)
        }
        
        return token . Format("{:08X}", A_TickCount)
    }
    
    ; Check if account is locked due to failed attempts
    static IsAccountLocked(username, clientIP) {
        key := username . "_" . clientIP
        if (!this.loginAttempts.Has(key))
            return false
            
        attempts := this.loginAttempts[key]
        if (attempts.count >= this.maxLoginAttempts) {
            ; Check if lockout period has expired (15 minutes)
            if (A_TickCount - attempts.lastAttempt < 900000) {
                return true
            } else {
                ; Reset attempts after lockout period
                this.loginAttempts.Delete(key)
                return false
            }
        }
        
        return false
    }
    
    ; Record failed login attempt
    static RecordFailedAttempt(username, clientIP) {
        key := username . "_" . clientIP
        if (!this.loginAttempts.Has(key)) {
            this.loginAttempts[key] := {count: 0, lastAttempt: 0}
        }
        
        this.loginAttempts[key].count++
        this.loginAttempts[key].lastAttempt := A_TickCount
    }
    
    ; Clear failed attempts on successful login
    static ClearFailedAttempts(username, clientIP) {
        key := username . "_" . clientIP
        if (this.loginAttempts.Has(key)) {
            this.loginAttempts.Delete(key)
        }
    }
    
    ; Log security events to audit trail
    static LogSecurityEvent(eventType, message) {
        try {
            timestamp := FormatTime(, "yyyy-MM-dd HH:mm:ss")
            logEntry := timestamp " [" eventType "] " message "`r`n"
            FileAppend(logEntry, this.auditLog)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to write security audit log: " err.Message)
        }
    }
    
    ; Encrypt sensitive data
    static EncryptData(data) {
        ; Simple XOR encryption - in production use proper encryption
        if (!data || data == "")
            return ""
            
        encrypted := ""
        keyLen := StrLen(this.encryptionKey)
        
        Loop StrLen(data) {
            keyChar := SubStr(this.encryptionKey, Mod(A_Index - 1, keyLen) + 1, 1)
            dataChar := SubStr(data, A_Index, 1)
            encryptedChar := Chr(Ord(dataChar) ^ Ord(keyChar))
            encrypted .= encryptedChar
        }
        
        return encrypted
    }
    
    ; Decrypt sensitive data
    static DecryptData(encryptedData) {
        ; XOR decryption (same as encryption for XOR)
        return this.EncryptData(encryptedData)
    }
    
    ; Generate encryption key from system properties
    static GenerateEncryptionKey() {
        ; Create key from system properties
        computerName := A_ComputerName
        userName := A_UserName
        scriptDir := A_ScriptDir
        
        combined := computerName . userName . scriptDir . "WinCBT2024"
        
        ; Create a simple key
        key := ""
        Loop 16 {
            charIndex := Mod(A_Index - 1, StrLen(combined)) + 1
            key .= SubStr(combined, charIndex, 1)
        }
        
        return key
    }
    
    ; Validate session and check timeout
    static ValidateSession() {
        if (!this.initialized)
            return false
            
        ; Check session timeout
        if (A_TickCount - this.lastActivity > this.sessionTimeout * 1000) {
            this.LogSecurityEvent("SESSION", "Session expired due to inactivity")
            return false
        }
        
        ; Update last activity
        this.lastActivity := A_TickCount
        return true
    }
    
    ; Secure file operations with validation
    static SecureFileWrite(filePath, content) {
        try {
            ; Validate file path
            if (!this.ValidateFilePath(filePath)) {
                this.LogSecurityEvent("SECURITY_VIOLATION", "Invalid file path attempted: " filePath)
                return false
            }
            
            ; Encrypt sensitive content
            encryptedContent := this.EncryptData(content)
            
            ; Write to file
            FileAppend(encryptedContent, filePath)
            this.LogSecurityEvent("FILE_WRITE", "Secure file write: " filePath)
            return true
        } catch as err {
            this.LogSecurityEvent("ERROR", "Secure file write failed: " err.Message)
            return false
        }
    }
    
    ; Validate file paths to prevent directory traversal
    static ValidateFilePath(filePath) {
        ; Check for directory traversal attempts
        if (InStr(filePath, "..") || InStr(filePath, "~") || InStr(filePath, "$"))
            return false
            
        ; Ensure path is within application directory
        appDir := A_ScriptDir
        if (!InStr(filePath, appDir))
            return false
            
        return true
    }
}
