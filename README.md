# WinCBT-Biometric and WinCBT-Admin

This repository contains two complementary applications for managing computer-based testing with biometric verification:

1. **WinCBT-Biometric**: An exam verification system built with AutoHotkey v2 for managing candidate verification and seat assignment
2. **WinCBT-Admin**: A centralized database management application for multiple WinCBT-Biometric installations

## System Architecture

The system is designed to work with multiple WinCBT-Biometric stations, each running on separate Windows computers, with a central database managed by WinCBT-Admin. The database can be accessed through an SMB network share from a central database server.

```
┌─────────────────┐
│  WinCBT-Admin   │
│  (Central DB    │
│   Management)   │
└────────┬────────┘
         │
         │ Network Share (SMB)
         │
┌────────┴────────┐
│                 │
│  Shared Database│
│                 │
└─┬───────┬───────┘
  │       │
  │       │
┌─▼─┐   ┌─▼─┐
│Bio│   │Bio│   Additional
│Stn│   │Stn│   Biometric
│ 1 │   │ 2 │   Stations...
└───┘   └───┘
```

## WinCBT-Biometric

WinCBT-Biometric is an exam verification system that manages candidate verification and seat assignment with support for both pre-exam and post-exam verification modes.

## Key Features

### Core Functionality
- **Candidate Search and Verification**: Fast roll number-based candidate lookup
- **Biometric Data Capture**: Photo, fingerprint (left/right), and signature capture
- **Automated Seat Assignment**: Intelligent seat allocation with special needs support
- **Dual Mode Operation**: Pre-exam and post-exam verification workflows
- **Special Candidate Support**: Accommodations for candidates with physical limitations
- **Comprehensive Logging**: Detailed audit trail and error tracking

### Advanced Features
- **Thumb Preference Consistency**: Maintains identical thumb configuration between pre-exam and post-exam modes
- **Automatic Verification**: AI-powered photo and signature verification with confidence thresholds
- **Hardware Integration**: SecuGen fingerprint scanners and webcam support
- **Network Database Support**: Multi-station deployment with shared database
- **Real-time Status Updates**: Live verification status tracking and reporting

## System Requirements

- Windows 10 or later
- AutoHotkey v2.0 or later
- FFmpeg for webcam functionality
- SecuGen fingerprint scanner (optional)

## Verification Workflows

### Pre-Exam Mode Workflow

The pre-exam verification process ensures candidates are properly verified before starting their exam:

1. **Candidate Search**
   - Enter roll number and click "Search"
   - System loads candidate information and biometric data
   - Camera activates for live verification

2. **Biometric Capture**
   - **Photo Capture**: Live webcam photo with automatic verification
   - **Fingerprint Capture**: Left thumb (and right thumb if enabled)
     - Special candidates: Thumb selection dialog for accommodation
     - Non-special candidates: Standard left/right thumb options
     - System saves thumb preference for post-exam consistency
   - **Signature Capture**: Digital signature verification (if enabled)

3. **Verification Process**
   - Automatic verification with confidence thresholds
   - Manual verification option for low-confidence results
   - Real-time status updates for each biometric type

4. **Seat Assignment**
   - Automatic seat allocation based on:
     - Special needs requirements (ground floor priority)
     - Room capacity and distribution
     - Exam-specific configurations
   - Seat assignment notification with floor, room, and seat details

### Post-Exam Mode Workflow

The post-exam verification ensures candidate identity after exam completion:

1. **Mode Activation**
   - Enable "Post-Exam Verification" in Settings
   - Application title shows "[Post-Exam]" indicator
   - Only candidates with pre-exam seat assignments are eligible

2. **Candidate Search**
   - Enter roll number of candidate who completed exam
   - System validates pre-exam seat assignment exists
   - Loads stored thumb preferences from pre-exam mode

3. **Biometric Re-verification**
   - **Photo Capture**: New photo compared against pre-exam captured photo
   - **Fingerprint Capture**: Uses identical thumb configuration from pre-exam
     - No thumb selection dialogs shown (automatic configuration)
     - Compares against pre-exam fingerprint templates
   - **Signature Capture**: Compared against registered signature

4. **Verification Completion**
   - All biometric data saved with "_post" suffix
   - Post-exam status fields updated independently
   - Comprehensive logging for audit trail
   - No seat assignment (candidate already seated)

### Thumb Preference Consistency

A critical feature ensuring data integrity between pre-exam and post-exam modes:

- **Pre-exam Mode**:
  - Special candidates select thumb preference via dialog
  - Non-special candidates use standard controls
  - System saves ThumbPreference to database

- **Post-exam Mode**:
  - **No thumb selection allowed** - maintains exact pre-exam configuration
  - System automatically reads stored ThumbPreference
  - Configures capture buttons based on stored preference
  - Ensures identical verification workflow

## WinCBT-Admin

WinCBT-Admin is a centralized database management application for WinCBT-Biometric installations.

### Features

- Candidate database management (import, export, editing)
- Operator account management
- Hardware configuration
- Room configuration
- Report generation
- System-wide settings

### Usage

1. Run `WinCBT-Admin.ahk` or `WinCBT-Admin.exe`
2. Log in with administrator credentials
3. Manage candidate database, operator accounts, and system settings
4. Generate reports on verification status and seat assignments

## Installation

1. Clone or download this repository
2. Ensure AutoHotkey v2 is installed
3. Run `WinCBT-Biometric.exe` or `WinCBT-Biometric.ahk` for the verification system
4. Run `WinCBT-Admin.ahk` or `WinCBT-Admin.exe` for the administration system

## File Naming Conventions

The system uses consistent file naming patterns to organize biometric data:

### Photo Files
- **Registered Photos**: `{rollNo}_registered_photo.jpg`
- **Pre-exam Captured**: `{rollNo}_captured_photo_pre.jpg`
- **Post-exam Captured**: `{rollNo}_captured_photo_post.jpg`

### Fingerprint Files
- **Pre-exam Templates**: `{rollNo}_captured_fingerprint_{left|right}_pre.fpt`
- **Post-exam Templates**: `{rollNo}_captured_fingerprint_{left|right}_post.fpt`
- **Fingerprint Images**: `{rollNo}_captured_fingerprint_{left|right}_{pre|post}.bmp`

### Signature Files
- **Registered Signatures**: `{rollNo}_registered_signature.jpg`
- **Pre-exam Captured**: `{rollNo}_captured_signature_pre.jpg`
- **Post-exam Captured**: `{rollNo}_captured_signature_post.jpg`

### Database Status Fields

#### Pre-exam Status Fields
- `PhotoStatus`: Photo verification status
- `FingerprintStatus`: Left fingerprint verification status
- `RightFingerprintStatus`: Right fingerprint verification status (if enabled)
- `SignatureStatus`: Signature verification status
- `BiometricStatus`: Overall biometric verification status
- `VerificationStatus`: Complete verification status
- `ThumbPreference`: Stored thumb configuration ("Left", "Right", "Both")

#### Post-exam Status Fields
- `PostExamPhotoStatus`: Post-exam photo verification status
- `PostExamFingerprintStatus`: Post-exam left fingerprint status
- `PostExamRightFingerprintStatus`: Post-exam right fingerprint status
- `PostExamSignatureStatus`: Post-exam signature status
- `PostExamBiometricStatus`: Overall post-exam biometric status

## Directory Structure

```
WinCBT-Biometric/
├── WinCBT-Biometric.ahk  # Main biometric application
├── WinCBT-Admin.ahk      # Main admin application
├── bin/                  # External binaries (FFmpeg)
├── db/                   # Database files
│   ├── candidates.ini    # Candidate information
│   ├── config.ini        # Database configuration
│   ├── hardware.ini      # Computer hardware mapping
│   ├── rooms.ini         # Room configurations
│   ├── seat_assignments.ini # Seat assignments
│   ├── operators.ini     # Operator accounts
│   ├── img/              # Candidate images
│   │   └── candidates/   # Biometric capture files
│   └── fpt/              # Fingerprint templates
├── img/                  # Default and temporary images
├── lib/                  # Library files
│   ├── biometric_functions.ahk  # Biometric capture functions
│   ├── camera_setup.ahk         # Camera configuration
│   ├── db_functions.ahk         # Database operations
│   ├── error_handler.ahk        # Error handling system
│   ├── secugen_wrapper.ahk      # Fingerprint scanner interface
│   ├── webcam_lib.ahk           # Webcam functionality
│   ├── post_exam_utils.ahk      # Post-exam verification utilities
│   ├── PathManager.ahk          # Centralized path management
│   ├── admin_db_functions.ahk   # Admin database functions
│   ├── admin_import_export.ahk  # Admin import/export functionality
│   ├── admin_operator_management.ahk # Admin operator management
│   ├── admin_reports.ahk        # Admin report generation
│   ├── admin_settings.ahk       # Admin settings management
│   └── admin_candidate_management.ahk # Admin candidate management
├── logs/                 # Application logs
└── tmp/                  # Temporary files
```

## Error Handling System

WinCBT-Biometric includes a comprehensive error handling system that:

1. **Validates Required Files and Directories**
   - Checks for existence of critical files and directories
   - Creates missing directories automatically
   - Generates default configuration files if needed

2. **Handles File Operations Safely**
   - Validates file existence before operations
   - Provides fallback mechanisms for missing files
   - Logs all file operations for troubleshooting

3. **Manages Hardware Failures**
   - Gracefully handles camera and fingerprint scanner failures
   - Provides fallback mechanisms when hardware is unavailable
   - Logs hardware status for diagnostics

4. **Logs Errors and Warnings**
   - Maintains detailed logs in `logs/error.log`
   - Categorizes messages by severity (INFO, WARNING, ERROR, CRITICAL)
   - Timestamps all log entries for tracking

## Configuration

### Application Configuration Files

Both applications use several INI files for configuration:

- **`WinCBT-Biometric.ini`**: Main application settings (verification modes, thresholds, hardware)
- **`db/config.ini`**: Database-specific settings and paths
- **`db/candidates.ini`**: Candidate information and verification status
- **`db/hardware.ini`**: Computer hardware mapping and device configuration
- **`db/rooms.ini`**: Room configurations and capacity settings
- **`db/seat_assignments.ini`**: Seat assignments and allocation tracking
- **`db/operators.ini`**: Operator account information and permissions

### Key Configuration Settings

#### Verification Settings
```ini
[Verification]
PhotoVerificationMode=Auto          # Auto, Manual, or Both
PhotoConfidenceThreshold=75         # Minimum confidence for auto-verification
FingerprintMode=compare             # save or compare
SignatureVerification=1             # Enable/disable signature verification
RightThumbprintVerification=1       # Enable/disable right thumb verification
```

#### Post-Exam Mode Settings
```ini
[General]
PostExamVerification=0              # Enable/disable post-exam mode
```

#### Camera Settings
```ini
[Camera]
DefaultCamera=0                     # Default camera index
CameraResolution=640x480           # Camera resolution
```

### Network Configuration

For multi-station deployments, configure the network settings:

```ini
[Network]
EnableNetworkSharing=1
SharedDatabasePath=\\server\share\db
RefreshInterval=300
```

### Special Candidate Configuration

Special candidates with physical limitations are supported through:
- **Special Flag**: Set `Special=1` in candidates.ini
- **Thumb Preference**: Automatically saved and maintained across modes
- **Accommodation Dialogs**: Pre-exam thumb selection with post-exam consistency

## Troubleshooting

### Common Issues and Solutions

#### Pre-Exam Mode Issues

**Problem**: Candidate search returns "Candidate Not Found"
- **Solution**: Verify roll number exists in `db/candidates.ini`
- **Check**: Ensure database path is correctly configured

**Problem**: Camera not working or shows "No Live Camera Feed"
- **Solution**: Check camera connection and permissions
- **Check**: Verify FFmpeg is installed in `bin/` directory
- **Fix**: Go to Settings > Camera to configure camera selection

**Problem**: Fingerprint scanner not detected
- **Solution**: Install SecuGen SDK and drivers
- **Check**: Verify device connection and USB permissions
- **Fix**: Restart application after connecting device

**Problem**: Seat assignment fails
- **Solution**: Check room configuration in `db/rooms.ini`
- **Check**: Verify seat capacity and availability
- **Fix**: Ensure all verifications are complete before assignment

#### Post-Exam Mode Issues

**Problem**: "No pre-exam seat found" error
- **Solution**: Candidate must have completed pre-exam verification
- **Check**: Verify seat assignment exists in `db/seat_assignments.ini`
- **Fix**: Complete pre-exam process first

**Problem**: Thumb selection dialog still appears in post-exam mode
- **Solution**: This should not happen - indicates a bug
- **Check**: Verify `ThumbPreference` is saved in candidates.ini
- **Fix**: Contact support if issue persists

**Problem**: Post-exam verification status not updating
- **Solution**: Check individual verification statuses
- **Check**: Verify all required biometrics are captured
- **Fix**: Ensure post-exam mode is properly enabled

#### General Troubleshooting Steps

1. **Check Log Files**: Review `logs/application.log` and `logs/error.log`
2. **Verify Directories**: Ensure all required directories exist and are writable
3. **Hardware Check**: Confirm camera and fingerprint scanner connections
4. **Configuration Review**: Validate all INI file settings
5. **Network Connectivity**: Test database share accessibility (multi-station setups)
6. **Permissions**: Verify application has read/write access to database files
7. **Restart Application**: Close and restart to reset hardware connections

### Debug Mode

Enable detailed logging by setting debug mode in configuration:
```ini
[Debug]
EnableDebugLogging=1
VerboseOutput=1
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- AutoHotkey community for documentation and support
- SecuGen for fingerprint scanner SDK
- FFmpeg for webcam capture functionality
- Contributors to the WinCBT project
