# WinCBT-Biometric Pre-Exam Verification Workflow

```mermaid
flowchart TD
    A[Enter Roll Number] --> B[Click Search Button]
    B --> C[Reset Interface & Load Data]
    C --> D{Candidate Found?}

    D -->|No| E[Show Not Found - Red]
    D -->|Yes| F{Check BiometricStatus}

    %% Already Verified Scenarios
    F -->|Verified| G{Has Seat Assignment?}
    G -->|Yes| H[Already Completed]
    G -->|No| I[Verified - Ready for Seat]

    H --> H1[Show Seat already assigned - Green]
    H1 --> H2[Display Seat Number]
    H2 --> H3[Disable All Controls]
    H3 --> END1[Ready for Next Search]

    I --> I1[Show Name - Green]
    I1 --> I2[Show All Status Verified]
    I2 --> I3[Enable Assign Seat Button]
    I3 --> ASSIGN[Seat Assignment]

    %% Fresh Candidate Flow
    F -->|Not Verified| J[Fresh Candidate]
    J --> J1[Show Name - Black]
    J1 --> J2[Enable Capture & Verification Groups]
    J2 --> J3[Activate Camera]
    J3 --> J4[Set All Status to Dash]
    J4 --> J5{Special=1?}

    %% Special Candidate Handling
    J5 -->|Yes| S1[Show Special Indicator]
    S1 --> S2[Show Thumb Selection Dialog]
    S2 --> S3[Configure Buttons Based on Selection]
    S3 --> S4[Hide Standard Thumb Controls]
    S4 --> READY[Ready for Verification]

    %% Regular Candidate
    J5 -->|No| REG1[Show Standard Thumb Controls]
    REG1 --> READY

    %% Verification Process - All Independent/Parallel
    READY --> VERIFY_READY[All Verification Options Available]

    %% Photo Verification - Independent
    VERIFY_READY --> P1{Click Capture Photo?}
    P1 -->|Yes| P2[Capture & Auto-Verify]
    P2 --> P3{Confidence >= 70%?}
    P3 -->|Yes| P4[Photo Status: Verified]
    P3 -->|No| P5[Photo Status: Needs Review]
    P5 --> P6{Manual Approve?}
    P6 -->|Yes| P4
    P6 -->|No| P1
    P4 --> CHECK[Check All Complete]

    %% Left Fingerprint - Independent with 3-Retry Logic
    VERIFY_READY --> F1{**Click Capture Left Thumb?**}
    F1 -->|**Yes**| F2[**Capture & Check Quality**<br/>**Attempt 1**]
    F2 --> F3{**Quality >= 80?**}
    F3 -->|**Yes**| F4[**Left Fingerprint: Saved**]
    F3 -->|**No**| F5[**Store Result & Retry**<br/>**Attempt 2**]
    F5 --> F6{**Quality >= 80?**}
    F6 -->|**Yes**| F4
    F6 -->|**No**| F7[**Store Result & Retry**<br/>**Attempt 3**]
    F7 --> F8{**Quality >= 80?**}
    F8 -->|**Yes**| F4
    F8 -->|**No**| F9[**Auto-Save Best of 3**<br/>**No Manual Verification**]
    F9 --> F4
    F4 --> CHECK

    %% Right Fingerprint - Independent with 3-Retry Logic (if enabled)
    VERIFY_READY --> R1{**Click Capture Right Thumb?**}
    R1 -->|**Yes**| R2[**Capture & Check Quality**<br/>**Attempt 1**]
    R2 --> R3{**Quality >= 80?**}
    R3 -->|**Yes**| R4[**Right Fingerprint: Saved**]
    R3 -->|**No**| R5[**Store Result & Retry**<br/>**Attempt 2**]
    R5 --> R6{**Quality >= 80?**}
    R6 -->|**Yes**| R4
    R6 -->|**No**| R7[**Store Result & Retry**<br/>**Attempt 3**]
    R7 --> R8{**Quality >= 80?**}
    R8 -->|**Yes**| R4
    R8 -->|**No**| R9[**Auto-Save Best of 3**<br/>**No Manual Verification**]
    R9 --> R4
    R4 --> CHECK

    %% Signature - Independent (if enabled)
    VERIFY_READY --> SIG1{**Click Capture Signature?**}
    SIG1 -->|**Yes**| SIG2[**Capture & Verify**]
    SIG2 --> SIG3[**Signature: Verified or Failed**]
    SIG3 --> CHECK

    %% Completion Check - Runs After Any Verification
    CHECK --> C1{**Photo Verified?**}
    C1 -->|**No**| C3[**Verification Status: Incomplete**]
    C1 -->|**Yes**| C2{**Check ThumbPreference**}

    %% ThumbPreference Logic
    C2 -->|**Left**| C4{**Left Thumb Verified?**}
    C2 -->|**Right**| C5{**Right Thumb Verified?**}
    C2 -->|**Both**| C6{**Both Thumbs Verified?**}
    C2 -->|**Unknown**| C7{**Special Candidate?**}

    %% Left Thumb Only Verification
    C4 -->|**Yes**| C8{**Signature Required?**}
    C4 -->|**No**| C3

    %% Right Thumb Only Verification
    C5 -->|**Yes**| C8
    C5 -->|**No**| C3

    %% Both Thumbs Verification
    C6 -->|**Yes**| C8
    C6 -->|**No**| C3

    %% Unknown Preference Handling
    C7 -->|**Yes**| C9{**At Least One Thumb Verified?**}
    C7 -->|**No**| C6

    %% Special Candidate - At Least One Thumb
    C9 -->|**Yes**| C8
    C9 -->|**No**| C3

    %% Final Signature Check
    C8 -->|**Not Required**| C10[**Verification Status: Completed**]
    C8 -->|**Required & Verified**| C10
    C8 -->|**Required & Not Verified**| C3

    C10 --> C11[**Enable Assign Seat Button**]
    C11 --> ASSIGN
    C3 --> VERIFY_READY

    %% Seat Assignment
    ASSIGN --> A1{Click Assign Seat?}
    A1 -->|Yes| A2[Assign Seat & Show Popup]
    A2 --> A3[Reset Interface]
    A3 --> END2[Ready for Next Search]

    %% Error States
    E --> END3[Error State]

    %% Styling - All text black and bold
    classDef success fill:#d4edda,stroke:#155724,color:#000000
    classDef error fill:#f8d7da,stroke:#721c24,color:#000000
    classDef process fill:#cce5ff,stroke:#004085,color:#000000
    classDef decision fill:#fff3cd,stroke:#856404,color:#000000
    classDef endpoint fill:#e2e3e5,stroke:#383d41,color:#000000
    classDef completion fill:#e8f5e8,stroke:#2d5a2d,color:#000000

    class P4,F4,F9,R4,R9,SIG3,C10,C11,I1,I2,H1,H2,A2 success
    class E,P5,C3 error
    class A,B,C,J1,J2,J3,J4,S1,S3,SIG2,REG1,P2,F2,F5,F7,R2,R5,R7,A3,VERIFY_READY process
    class D,F,G,J5,P1,P3,P6,F1,F3,F6,F8,R1,R3,R6,R8,SIG1,C1,C2,C4,C5,C6,C7,C8,C9,A1 decision
    class END1,END2,END3 endpoint
```

## Workflow Summary

### Main Scenarios:
1. **Fresh Candidate**: Complete verification workflow
2. **Verified + No Seat**: Direct to seat assignment
3. **Already Assigned**: Read-only display
4. **Error Cases**: Not found or inactive

### ✅ **Key Insight: Parallel Verification**
**All verification steps are INDEPENDENT and can be done in ANY ORDER:**
- **Photo Verification**: Can be done first, last, or anytime
- **Left Fingerprint**: Independent of photo or signature
- **Right Fingerprint**: Independent of other verifications (if enabled)
- **Signature**: Independent of biometric verifications (if enabled)

### Verification Requirements:
- **Photo**: Auto-verify if confidence >=70%, else manual review
- **Left Fingerprint**: 3-retry system with automatic best-of-three selection:
  - **Attempt 1-3**: Try to achieve quality >=80%
  - **After 3 attempts**: Automatically save best result (no manual verification)
  - **Status**: Always "Saved" regardless of final quality
- **Right Fingerprint**: Same 3-retry system as left fingerprint (if enabled for candidate)
- **Signature**: Verification required (if signature verification enabled)

### Special Candidate Handling:
- **Thumb Selection Dialog**: Shows IMMEDIATELY after search (not during capture)
- **Button Configuration**: Based on dialog selection (Left/Right/Both)
- **Control Visibility**: Standard thumb controls hidden for special candidates

### Completion Logic:
- **CheckAllVerifications()** runs after EACH individual verification
- **Photo Verification**: Always required for completion
- **Thumb Verification**: Based on ThumbPreference setting:
  - **"Left"**: Only left thumb must be verified
  - **"Right"**: Only right thumb must be verified
  - **"Both"**: Both left AND right thumbs must be verified
  - **Unknown + Special**: At least one thumb must be verified
  - **Unknown + Regular**: Both thumbs must be verified (default to "Both")
- **Signature Verification**: Required only if enabled in configuration
- **Seat Assignment Enabled**: When ALL required verifications complete based on above logic
- **Status Updates**: Real-time after each verification step

### Visual Indicators:
- **Green**: Success/Completed states
- **Red**: Error/Failed states
- **Gray**: Disabled/Pending states
- **Blue**: Active/Processing states
