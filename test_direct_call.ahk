#Requires AutoHotkey v2.0

; Test script to directly call CheckAndUpdateBiometricStatus function
; This script tests the actual function from the main application

; Include necessary files
#Include lib\PathManager.ahk
#Include lib\error_handler.ahk

; Initialize error handler
ErrorHandler.Initialize("test_direct_call.log", "DEBUG")

; Simple output function
TestOutput(message) {
    OutputDebug(message)
    ErrorHandler.LogMessage("INFO", message)
    ; Also write to a simple text file
    try {
        FileAppend(message "`n", "test_direct_results.txt")
    } catch {
        ; Ignore file errors
    }
}

; We need to include the LoadCandidateData function from the main script
; Let's copy the essential parts here for testing

; LoadCandidateData function (copied from main script)
LoadCandidateData(rollNumber) {
    try {
        ; Get database file path
        candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
        
        ; Check if file exists
        if (!FileExist(candidatesPath)) {
            ErrorHandler.LogMessage("ERROR", "LoadCandidateData: Candidates file not found: " candidatesPath)
            return {}
        }
        
        ; Check if candidate section exists
        try {
            testRead := IniRead(candidatesPath, rollNumber, "Name", "")
            if (testRead == "") {
                ErrorHandler.LogMessage("DEBUG", "LoadCandidateData: Candidate not found: " rollNumber)
                return {}
            }
        } catch {
            ErrorHandler.LogMessage("DEBUG", "LoadCandidateData: Error reading candidate: " rollNumber)
            return {}
        }
        
        ; Read all candidate data fields
        candidateData := {}
        candidateData.RollNumber := rollNumber
        candidateData.Name := IniRead(candidatesPath, rollNumber, "Name", "")
        candidateData.FatherName := IniRead(candidatesPath, rollNumber, "FatherName", "")
        candidateData.Gender := IniRead(candidatesPath, rollNumber, "Gender", "")
        candidateData.DateOfBirth := IniRead(candidatesPath, rollNumber, "DateOfBirth", "")
        candidateData.PreferredLanguage := IniRead(candidatesPath, rollNumber, "PreferredLanguage", "")
        candidateData.Special := IniRead(candidatesPath, rollNumber, "Special", "0")
        candidateData.Status := IniRead(candidatesPath, rollNumber, "Status", "")
        candidateData.ExamID := IniRead(candidatesPath, rollNumber, "ExamID", "")
        candidateData.Picture := IniRead(candidatesPath, rollNumber, "Picture", "")
        candidateData.Signature := IniRead(candidatesPath, rollNumber, "Signature", "")
        
        ; Read verification status fields
        candidateData.PhotoStatus := IniRead(candidatesPath, rollNumber, "PhotoStatus", "")
        candidateData.FingerprintStatus := IniRead(candidatesPath, rollNumber, "FingerprintStatus", "")
        candidateData.RightFingerprintStatus := IniRead(candidatesPath, rollNumber, "RightFingerprintStatus", "")
        candidateData.SignatureStatus := IniRead(candidatesPath, rollNumber, "SignatureStatus", "")
        candidateData.BiometricStatus := IniRead(candidatesPath, rollNumber, "BiometricStatus", "")
        candidateData.ThumbPreference := IniRead(candidatesPath, rollNumber, "ThumbPreference", "")
        
        ; Read post-exam status fields
        candidateData.PostExamPhotoStatus := IniRead(candidatesPath, rollNumber, "PostExamPhotoStatus", "")
        candidateData.PostExamFingerprintStatus := IniRead(candidatesPath, rollNumber, "PostExamFingerprintStatus", "")
        candidateData.PostExamRightFingerprintStatus := IniRead(candidatesPath, rollNumber, "PostExamRightFingerprintStatus", "")
        candidateData.PostExamSignatureStatus := IniRead(candidatesPath, rollNumber, "PostExamSignatureStatus", "")
        candidateData.PostExamBiometricStatus := IniRead(candidatesPath, rollNumber, "PostExamBiometricStatus", "")
        
        ; Read verify mode status fields
        candidateData.VerifyPhotoStatus := IniRead(candidatesPath, rollNumber, "VerifyPhotoStatus", "")
        candidateData.VerifyFingerprintStatus := IniRead(candidatesPath, rollNumber, "VerifyFingerprintStatus", "")
        candidateData.VerifyRightFingerprintStatus := IniRead(candidatesPath, rollNumber, "VerifyRightFingerprintStatus", "")
        candidateData.VerifySignatureStatus := IniRead(candidatesPath, rollNumber, "VerifySignatureStatus", "")
        candidateData.VerifyBiometricStatus := IniRead(candidatesPath, rollNumber, "VerifyBiometricStatus", "")
        
        ; Read confidence levels
        candidateData.PhotoConfidence := IniRead(candidatesPath, rollNumber, "PhotoConfidence", "")
        candidateData.FingerprintConfidence := IniRead(candidatesPath, rollNumber, "FingerprintConfidence", "")
        candidateData.RightFingerprintConfidence := IniRead(candidatesPath, rollNumber, "RightFingerprintConfidence", "")
        candidateData.SignatureConfidence := IniRead(candidatesPath, rollNumber, "SignatureConfidence", "")
        
        ErrorHandler.LogMessage("DEBUG", "LoadCandidateData: Successfully loaded data for " rollNumber)
        ErrorHandler.LogMessage("DEBUG", "LoadCandidateData: ThumbPreference=" candidateData.ThumbPreference ", PhotoStatus=" candidateData.PhotoStatus ", FingerprintStatus=" candidateData.FingerprintStatus ", RightFingerprintStatus=" candidateData.RightFingerprintStatus)
        
        return candidateData
        
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "LoadCandidateData: Error loading candidate data for " rollNumber ": " err.Message)
        return {}
    }
}

; CheckAndUpdateBiometricStatus function (copied from main script)
CheckAndUpdateBiometricStatus(rollNumber) {
    try {
        ; Simulate global variables for testing
        SignatureVerificationEnabled := false  ; Disable signature verification for testing
        g_isPostExamMode := false
        g_isVerifyMode := false

        ; Load fresh candidate data from database
        candidateData := LoadCandidateData(rollNumber)
        if (!candidateData || candidateData.Name == "") {
            ErrorHandler.LogMessage("ERROR", "CheckAndUpdateBiometricStatus: Invalid candidate data for " rollNumber)
            return false
        }

        ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: Processing candidate " rollNumber " (" candidateData.Name ")")

        if (g_isPostExamMode) {
            ; Post-exam mode: Use existing complex logic (maintain compatibility)
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: Post-exam mode - delegating to existing logic")
            return false
        } else if (g_isVerifyMode) {
            ; For verify mode, use the existing complex logic (maintain compatibility)
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: Verify mode - delegating to existing logic")
            return false
        } else {
            ; Pre-exam mode: Use data-driven ThumbPreference logic
            photoVerified := (candidateData.PhotoStatus == "Verified")
            signatureVerified := (!SignatureVerificationEnabled || candidateData.SignatureStatus == "Verified")

            ; Check thumbprint verification based on ThumbPreference
            thumbPreference := candidateData.ThumbPreference
            if (thumbPreference == "") {
                thumbPreference := "Both"  ; Default to Both if not set
            }

            leftThumbVerified := (candidateData.FingerprintStatus == "Saved" || candidateData.FingerprintStatus == "Verified")
            rightThumbVerified := (candidateData.RightFingerprintStatus == "Saved" || candidateData.RightFingerprintStatus == "Verified")

            thumbprintVerified := false
            if (thumbPreference == "Both") {
                ; Both thumbs must be verified
                thumbprintVerified := (leftThumbVerified && rightThumbVerified)
                ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: ThumbPreference=Both: left=" leftThumbVerified ", right=" rightThumbVerified ", result=" thumbprintVerified)
            } else if (thumbPreference == "Left") {
                ; Only left thumb must be verified
                thumbprintVerified := leftThumbVerified
                ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: ThumbPreference=Left: left=" leftThumbVerified ", result=" thumbprintVerified)
            } else if (thumbPreference == "Right") {
                ; Only right thumb must be verified
                thumbprintVerified := rightThumbVerified
                ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: ThumbPreference=Right: right=" rightThumbVerified ", result=" thumbprintVerified)
            } else {
                ; Unknown preference, default to Both
                thumbprintVerified := (leftThumbVerified && rightThumbVerified)
                ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: ThumbPreference=" thumbPreference " (unknown), defaulting to Both, result=" thumbprintVerified)
            }

            ; Determine overall verification status
            allVerified := (photoVerified && thumbprintVerified && signatureVerified)

            ; Enhanced debugging
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus DETAILED: rollNumber=" rollNumber)
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus DETAILED: photoVerified=" photoVerified " (PhotoStatus=" candidateData.PhotoStatus ")")
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus DETAILED: thumbprintVerified=" thumbprintVerified " (ThumbPreference=" thumbPreference ")")
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus DETAILED: leftThumbVerified=" leftThumbVerified " (FingerprintStatus=" candidateData.FingerprintStatus ")")
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus DETAILED: rightThumbVerified=" rightThumbVerified " (RightFingerprintStatus=" candidateData.RightFingerprintStatus ")")
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus DETAILED: signatureVerified=" signatureVerified " (SignatureVerificationEnabled=" SignatureVerificationEnabled ", SignatureStatus=" candidateData.SignatureStatus ")")
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus DETAILED: allVerified=" allVerified)

            ; Get database file path
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
            ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: Using database path: " candidatesPath)

            ; Update BiometricStatus in database based on verification result
            if (allVerified) {
                ; Set BiometricStatus to Verified
                IniWrite("Verified", candidatesPath, rollNumber, "BiometricStatus")
                ErrorHandler.LogMessage("INFO", "BiometricStatus set to Verified for candidate: " rollNumber " (ThumbPreference: " thumbPreference ")")

                ; Verify the write was successful
                verifyStatus := IniRead(candidatesPath, rollNumber, "BiometricStatus", "")
                ErrorHandler.LogMessage("DEBUG", "BiometricStatus verification after write: " verifyStatus)

                return true
            } else {
                ; Keep BiometricStatus as incomplete or remove it
                currentStatus := IniRead(candidatesPath, rollNumber, "BiometricStatus", "")
                ErrorHandler.LogMessage("DEBUG", "CheckAndUpdateBiometricStatus: Current BiometricStatus before update: " currentStatus)

                if (currentStatus == "Verified") {
                    ; Remove the Verified status since requirements are no longer met
                    IniWrite("", candidatesPath, rollNumber, "BiometricStatus")
                    ErrorHandler.LogMessage("INFO", "BiometricStatus removed (was Verified) for candidate: " rollNumber " - requirements no longer met")
                }

                return false
            }
        }

    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error in CheckAndUpdateBiometricStatus: " err.Message)
        return false
    }
}

; Test function
TestDirectCall() {
    ; Test cases
    testCases := [
        {rollNumber: "9350", description: "Regular candidate with ThumbPreference=Both, all verified"},
        {rollNumber: "9351", description: "Special candidate with ThumbPreference=Right, right thumb verified"}
    ]
    
    for testCase in testCases {
        TestOutput("Testing: " testCase.description)
        
        ; Call the actual function
        result := CheckAndUpdateBiometricStatus(testCase.rollNumber)
        TestOutput("CheckAndUpdateBiometricStatus result: " result)
        
        ; Check the database to see if BiometricStatus was set
        candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
        biometricStatus := IniRead(candidatesPath, testCase.rollNumber, "BiometricStatus", "")
        TestOutput("BiometricStatus in database: " biometricStatus)
        
        TestOutput("---")
    }
}

; Run the test
TestOutput("Starting direct call test...")
TestDirectCall()
TestOutput("Test completed. Check test_direct_results.txt and test_direct_call.log for results.")

; Show results
MsgBox("Test completed. Check test_direct_results.txt and test_direct_call.log for results.", "Test Complete")
