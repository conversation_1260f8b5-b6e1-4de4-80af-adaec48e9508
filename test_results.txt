Starting biometric status test...
Testing: Regular candidate with ThumbPreference=Both, all verified
Loaded candidate: <PERSON><PERSON>
ThumbPreference: Both
PhotoStatus: Verified
FingerprintStatus: Verified
RightFingerprintStatus: Verified
SignatureStatus: Verified
DEBUG: ThumbPreference=Both: left=1, right=1, result=1
DEBUG: Final verification: photo=1, thumbs=1, signature=1, all=1
Biometric status result: Verified
---
Testing: Special candidate with ThumbPreference=Right, right thumb verified
Loaded candidate: <PERSON><PERSON>
ThumbPreference: Right
PhotoStatus: Verified
FingerprintStatus: 
RightFingerprintStatus: Verified
SignatureStatus: Verified
DEBUG: ThumbPreference=Right: right=1, result=1
DEBUG: Final verification: photo=1, thumbs=1, signature=1, all=1
Biometric status result: Verified
---
Testing: Candidate with no verification status
Loaded candidate: <PERSON><PERSON> Sharma
ThumbPreference: 
PhotoStatus: 
FingerprintStatus: 
RightFingerprintStatus: 
SignatureStatus: 
DEBUG: ThumbPreference=Both: left=0, right=0, result=0
DEBUG: Final verification: photo=0, thumbs=0, signature=1, all=0
Biometric status result: Incomplete
---
Test completed. Check test_results.txt for results.
